/**
 * 增强的遮挡剔除系统
 * 提供高效的遮挡剔除算法，减少不可见对象的渲染
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import type { Camera   } from '../Camera';
import { Scene } from '../../scene/Scene';
import type { Transform } from '../../scene/Transform';
import { OcclusionCullingSystem, OcclusionCullingAlgorithm, OcclusionCullingSystemOptions } from './OcclusionCullingSystem';
import { Debug } from '../../utils/Debug';
import { EventEmitter, EventCallback } from '../../utils/EventEmitter';
import { PerformanceMonitor, PerformanceMetricType } from '../../utils/PerformanceMonitor';

/**
 * 增强的遮挡剔除系统事件类型
 */
export enum EnhancedOcclusionCullingSystemEventType {
  /** 实体被剔除 */
  ENTITY_CULLED = 'entity_culled',
  /** 实体被恢复 */
  ENTITY_RESTORED = 'entity_restored',
  /** 组件添加 */
  COMPONENT_ADDED = 'component_added',
  /** 组件移除 */
  COMPONENT_REMOVED = 'component_removed',
  /** 算法变更 */
  ALGORITHM_CHANGED = 'algorithm_changed',
  /** 性能统计更新 */
  STATS_UPDATED = 'stats_updated'
}

/**
 * 增强的遮挡剔除系统配置接口
 */
export interface EnhancedOcclusionCullingSystemOptions extends OcclusionCullingSystemOptions {
  /** 是否使用自适应算法选择 */
  useAdaptiveAlgorithm?: boolean;
  /** 是否使用多级遮挡剔除 */
  useMultiLevelCulling?: boolean;
  /** 是否使用预测剔除 */
  usePredictiveCulling?: boolean;
  /** 是否使用时间一致性 */
  useTemporalCoherence?: boolean;
  /** 是否使用GPU加速 */
  useGPUAcceleration?: boolean;
  /** 是否使用保守剔除 */
  useConservativeCulling?: boolean;
  /** 是否收集性能统计 */
  collectStats?: boolean;
  /** 是否使用自动优化 */
  useAutoOptimization?: boolean;
  /** 自动优化间隔（毫秒） */
  autoOptimizationInterval?: number;
}

/**
 * 遮挡剔除性能统计
 */
export interface OcclusionCullingStats {
  /** 总对象数量 */
  totalObjects: number;
  /** 剔除对象数量 */
  culledObjects: number;
  /** 剔除率 */
  cullingRate: number;
  /** 剔除时间（毫秒） */
  cullingTime: number;
  /** 渲染时间（毫秒） */
  renderTime: number;
  /** 总时间（毫秒） */
  totalTime: number;
  /** 算法类型 */
  algorithm: OcclusionCullingAlgorithm;
  /** 时间戳 */
  timestamp: number;
}

/**
 * 增强的遮挡剔除系统
 * 提供高效的遮挡剔除算法，减少不可见对象的渲染
 */
export class EnhancedOcclusionCullingSystem extends OcclusionCullingSystem {
  /** 系统类型 */
  public static readonly TYPE: string = 'EnhancedOcclusionCullingSystem';

  /** 是否使用自适应算法选择 */
  private useAdaptiveAlgorithm: boolean;
  /** 是否使用多级遮挡剔除 */
  private useMultiLevelCulling: boolean;
  /** 是否使用预测剔除 */
  private usePredictiveCulling: boolean;
  /** 是否使用时间一致性（重写基类属性） */
  protected useTemporalCoherence: boolean = true;
  /** 是否使用GPU加速 */
  private useGPUAcceleration: boolean;
  /** 是否使用保守剔除 */
  private useConservativeCulling: boolean;
  /** 是否收集性能统计 */
  private collectStats: boolean;
  /** 是否使用自动优化 */
  private useAutoOptimization: boolean;
  /** 自动优化间隔（毫秒） */
  private autoOptimizationInterval: number;
  /** 上次自动优化时间 */
  private lastAutoOptimizationTime: number;
  /** 性能统计历史 */
  private statsHistory: OcclusionCullingStats[];
  /** 最大历史记录数 */
  private maxHistoryLength: number;
  /** 上一帧剔除的实体 */
  private previousCulledEntities: Set<Entity>;
  /** 预测的相机位置 */
  private predictedCameraPosition: THREE.Vector3;
  /** 相机速度 */
  private cameraVelocity: THREE.Vector3;
  /** 上一帧相机位置 */
  private previousCameraPosition: THREE.Vector3;
  /** 事件发射器 */
  private eventEmitter: EventEmitter;
  /** 增强的层次Z缓冲区 */
  private enhancedHierarchicalZBuffer: Float32Array | null;
  /** 增强层次Z缓冲区宽度 */
  private enhancedHzbWidth: number;
  /** 增强层次Z缓冲区高度 */
  private enhancedHzbHeight: number;
  /** 增强层次Z缓冲区级别数 */
  private enhancedHzbLevels: number;
  /** 剔除时间 */
  private cullingTime: number = 0;
  /** 剔除的实体集合 */
  private culledEntities: Set<Entity> = new Set();
  /** 可剔除组件映射 */
  private cullableComponents: Map<Entity, any> = new Map();

  /**
   * 创建增强的遮挡剔除系统
   * @param options 系统选项
   */
  constructor(options: EnhancedOcclusionCullingSystemOptions = {}) {
    super(options);

    // 设置增强选项
    this.useAdaptiveAlgorithm = options.useAdaptiveAlgorithm !== undefined ? options.useAdaptiveAlgorithm : true;
    this.useMultiLevelCulling = options.useMultiLevelCulling !== undefined ? options.useMultiLevelCulling : true;
    this.usePredictiveCulling = options.usePredictiveCulling !== undefined ? options.usePredictiveCulling : true;
    this.useTemporalCoherence = options.useTemporalCoherence !== undefined ? options.useTemporalCoherence : true;
    this.useGPUAcceleration = options.useGPUAcceleration !== undefined ? options.useGPUAcceleration : true;
    this.useConservativeCulling = options.useConservativeCulling !== undefined ? options.useConservativeCulling : false;
    this.collectStats = options.collectStats !== undefined ? options.collectStats : true;
    this.useAutoOptimization = options.useAutoOptimization !== undefined ? options.useAutoOptimization : true;
    this.autoOptimizationInterval = options.autoOptimizationInterval || 5000;
    this.lastAutoOptimizationTime = 0;
    this.statsHistory = [];
    this.maxHistoryLength = 100;
    this.previousCulledEntities = new Set<Entity>();
    this.predictedCameraPosition = new THREE.Vector3();
    this.cameraVelocity = new THREE.Vector3();
    this.previousCameraPosition = new THREE.Vector3();
    this.eventEmitter = new EventEmitter();
    this.enhancedHierarchicalZBuffer = null;
    this.enhancedHzbWidth = 0;
    this.enhancedHzbHeight = 0;
    this.enhancedHzbLevels = 0;

    // 如果使用GPU加速，检查是否支持
    if (this.useGPUAcceleration) {
      this.checkGPUSupport();
    }

    // 如果使用层次Z缓冲区算法，初始化层次Z缓冲区
    if (this.algorithm === OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER) {
      this.initializeEnhancedHierarchicalZBuffer();
    }
  }

  /**
   * 获取系统类型
   * @returns 系统类型
   */
  public getType(): string {
    return EnhancedOcclusionCullingSystem.TYPE;
  }

  /**
   * 获取活动相机
   * @returns 活动相机
   */
  private getActiveCamera(): Camera | null {
    // 通过world获取相机
    if (this.world) {
      // 假设有一个相机系统或者相机实体
      const entities = this.world.getEntities();
      for (const entity of entities.values()) {
        const cameraComponent = entity.getComponent('Camera');
        if (cameraComponent) {
          return cameraComponent as Camera;
        }
      }
    }
    return null;
  }

  /**
   * 获取活动场景
   * @returns 活动场景
   */
  private getActiveScene(): Scene | null {
    // 通过world获取场景
    if (this.world) {
      // 假设world有场景引用
      return (this.world as any).scene || null;
    }
    return null;
  }

  /**
   * 执行主要遮挡剔除
   * @param camera 相机
   * @param scene 场景
   */
  private performMainOcclusionCulling(camera: Camera, scene: Scene): void {
    // 调用基类的遮挡剔除方法
    switch (this.algorithm) {
      case OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER:
        this.cullWithHierarchicalZBuffer(camera, scene);
        break;
      case OcclusionCullingAlgorithm.OCCLUSION_QUERY:
        this.cullWithOcclusionQuery(camera, scene);
        break;
      default:
        // 使用默认的遮挡剔除
        this.performBasicOcclusionCulling(camera, scene);
        break;
    }
  }

  /**
   * 执行基本遮挡剔除
   * @param camera 相机
   * @param scene 场景
   */
  private performBasicOcclusionCulling(camera: Camera, scene: Scene): void {
    // 基本的遮挡剔除实现
    if (!this.world) {
      return;
    }

    // 获取所有可剔除的实体
    const entities = this.world.getEntities();
    for (const entity of entities.values()) {
      const cullableComponent = entity.getComponent('CullableComponent');
      if (cullableComponent) {
        // 简单的距离剔除
        const transform = entity.getComponent('Transform') as Transform;
        if (transform) {
          const position = transform.getWorldPosition();
          const cameraPosition = camera.getThreeCamera().position;
          const distance = position.distanceTo(cameraPosition);

          // 如果距离太远，则剔除
          if (distance > 1000) {
            this.culledEntities.add(entity);
            this.cullableComponents.set(entity, cullableComponent);
          }
        }
      }
    }
  }

  /**
   * 检查是否有调试可视化
   * @returns 是否有调试可视化
   */
  private hasDebugVisualization(): boolean {
    // 检查是否启用了调试可视化
    return false; // 暂时禁用调试可视化
  }

  /**
   * 安全地更新调试可视化
   */
  private updateDebugVisualizationSafe(): void {
    // 安全地更新调试可视化
    try {
      // 这里可以添加调试可视化的更新逻辑
    } catch (error) {
      Debug.warn('EnhancedOcclusionCullingSystem', '调试可视化更新失败:', error);
    }
  }

  /**
   * 初始化系统
   */
  public initialize(): void {
    super.initialize();

    // 如果使用自适应算法选择，选择最佳算法
    if (this.useAdaptiveAlgorithm) {
      this.selectBestAlgorithm();
    }
  }

  /**
   * 更新系统
   * @param deltaTime 帧间隔时间（秒）
   */
  public update(deltaTime: number): void {
    if (!this.isEnabled()) {
      return;
    }

    // 获取相机和场景（使用基类的公共方法或通过world获取）
    const camera = this.getActiveCamera();
    const scene = this.getActiveScene();

    if (!camera || !scene) {
      return;
    }

    // 记录开始时间
    const startTime = performance.now();

    // 更新相机信息
    this.updateCameraInfo(camera, deltaTime);

    // 如果使用自适应算法选择，定期选择最佳算法
    if (this.useAutoOptimization && performance.now() - this.lastAutoOptimizationTime > this.autoOptimizationInterval) {
      this.optimizeSettings();
      this.lastAutoOptimizationTime = performance.now();
    }

    // 如果使用多级遮挡剔除，先进行粗略剔除
    if (this.useMultiLevelCulling) {
      this.performCoarseCulling(camera, scene);
    }

    // 如果使用预测剔除，预测相机位置并进行剔除
    if (this.usePredictiveCulling) {
      this.performPredictiveCulling(camera, scene, deltaTime);
    }

    // 执行主要遮挡剔除
    this.performMainOcclusionCulling(camera, scene);

    // 记录结束时间
    const endTime = performance.now();
    this.cullingTime = endTime - startTime;

    // 如果收集性能统计，更新统计信息
    if (this.collectStats) {
      this.updateStats(endTime - startTime);
    }

    // 更新调试可视化
    if (this.hasDebugVisualization()) {
      this.updateDebugVisualizationSafe();
    }

    // 保存当前剔除的实体，用于时间一致性
    this.previousCulledEntities = new Set(this.culledEntities);
  }

  /**
   * 更新相机信息
   * @param camera 相机
   * @param deltaTime 帧间隔时间（秒）
   */
  private updateCameraInfo(camera: Camera, deltaTime: number): void {
    // 获取相机位置
    const cameraPosition = camera.getThreeCamera().position;

    // 如果有上一帧相机位置，计算相机速度
    if (this.previousCameraPosition.lengthSq() > 0) {
      this.cameraVelocity.copy(cameraPosition).sub(this.previousCameraPosition);
      if (deltaTime > 0) {
        this.cameraVelocity.divideScalar(deltaTime);
      }
    }

    // 保存当前相机位置
    this.previousCameraPosition.copy(cameraPosition);

    // 预测下一帧相机位置
    this.predictedCameraPosition.copy(cameraPosition).add(this.cameraVelocity.clone().multiplyScalar(deltaTime));
  }

  /**
   * 检查GPU支持
   */
  private checkGPUSupport(): void {
    // 检查是否支持WebGL2
    const canvas = document.createElement('canvas');
    const gl = canvas.getContext('webgl2');

    if (!gl) {
      Debug.warn('EnhancedOcclusionCullingSystem', '不支持WebGL2，已禁用GPU加速');
      this.useGPUAcceleration = false;
      return;
    }

    // 检查是否支持所需的扩展
    const extensions = [
      'EXT_color_buffer_float',
      'OES_texture_float_linear'
    ];

    for (const ext of extensions) {
      if (!gl.getExtension(ext)) {
        Debug.warn('EnhancedOcclusionCullingSystem', `不支持扩展 ${ext}，已禁用GPU加速`);
        this.useGPUAcceleration = false;
        return;
      }
    }
  }

  /**
   * 初始化增强层次Z缓冲区
   */
  private initializeEnhancedHierarchicalZBuffer(): void {
    // 获取渲染器（通过引擎或其他方式）
    const renderer = this.getRendererSafe();
    if (!renderer) {
      // 使用默认尺寸
      this.enhancedHzbWidth = 1024;
      this.enhancedHzbHeight = 768;
    } else {
      // 获取渲染器大小
      const size = renderer.getSize(new THREE.Vector2());
      this.enhancedHzbWidth = size.width;
      this.enhancedHzbHeight = size.height;
    }

    // 计算层次Z缓冲区级别数
    this.enhancedHzbLevels = Math.floor(Math.log2(Math.max(this.enhancedHzbWidth, this.enhancedHzbHeight))) + 1;

    // 创建层次Z缓冲区
    const totalSize = this.calculateTotalHZBSize(this.enhancedHzbWidth, this.enhancedHzbHeight, this.enhancedHzbLevels);
    this.enhancedHierarchicalZBuffer = new Float32Array(totalSize);

    Debug.log('EnhancedOcclusionCullingSystem', `初始化增强层次Z缓冲区: ${this.enhancedHzbWidth}x${this.enhancedHzbHeight}, ${this.enhancedHzbLevels}级, ${totalSize}个元素`);
  }

  /**
   * 安全地获取渲染器
   * @returns 渲染器或null
   */
  private getRendererSafe(): THREE.WebGLRenderer | null {
    // 尝试通过引擎获取渲染器
    if (this.engine && (this.engine as any).renderer) {
      return (this.engine as any).renderer;
    }

    // 尝试通过全局变量获取
    if (typeof window !== 'undefined' && (window as any).renderer) {
      return (window as any).renderer;
    }

    return null;
  }

  /**
   * 计算层次Z缓冲区总大小
   * @param width 宽度
   * @param height 高度
   * @param levels 级别数
   * @returns 总大小
   */
  private calculateTotalHZBSize(width: number, height: number, levels: number): number {
    let totalSize = 0;
    let levelWidth = width;
    let levelHeight = height;

    for (let i = 0; i < levels; i++) {
      totalSize += levelWidth * levelHeight;
      levelWidth = Math.max(1, Math.floor(levelWidth / 2));
      levelHeight = Math.max(1, Math.floor(levelHeight / 2));
    }

    return totalSize;
  }

  /**
   * 选择最佳算法
   */
  private selectBestAlgorithm(): void {
    // 获取性能监控器
    const performanceMonitor = PerformanceMonitor.getInstance();

    // 使用可用的性能指标方法
    const gpuMetric = performanceMonitor.getMetric(PerformanceMetricType.GPU_USAGE);
    const cpuMetric = performanceMonitor.getMetric(PerformanceMetricType.CPU_USAGE);

    const gpuPerformance = gpuMetric ? gpuMetric.value / 100 : 0.5; // 默认值
    const cpuPerformance = cpuMetric ? cpuMetric.value / 100 : 0.5; // 默认值

    // 根据性能选择最佳算法
    let bestAlgorithm: OcclusionCullingAlgorithm;

    if (this.useGPUAcceleration && gpuPerformance < 0.7) {
      // 如果GPU性能良好且支持GPU加速，使用硬件遮挡查询
      bestAlgorithm = OcclusionCullingAlgorithm.HARDWARE_OCCLUSION_QUERY;
    } else if (cpuPerformance < 0.7) {
      // 如果CPU性能良好，使用层次Z缓冲区
      bestAlgorithm = OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER;
    } else {
      // 如果性能不佳，使用简单的遮挡查询
      bestAlgorithm = OcclusionCullingAlgorithm.OCCLUSION_QUERY;
    }

    // 如果算法发生变化，更新算法
    if (bestAlgorithm !== this.algorithm) {
      const oldAlgorithm = this.algorithm;
      this.algorithm = bestAlgorithm;

      // 如果新算法是层次Z缓冲区，初始化层次Z缓冲区
      if (bestAlgorithm === OcclusionCullingAlgorithm.HIERARCHICAL_Z_BUFFER && !this.enhancedHierarchicalZBuffer) {
        this.initializeHierarchicalZBuffer();
      }

      // 发出算法变更事件
      this.eventEmitter.emit(EnhancedOcclusionCullingSystemEventType.ALGORITHM_CHANGED, bestAlgorithm, oldAlgorithm);
      Debug.log('EnhancedOcclusionCullingSystem', `算法变更: ${oldAlgorithm} -> ${bestAlgorithm}`);
    }
  }

  /**
   * 优化设置
   */
  private optimizeSettings(): void {
    // 获取性能监控器
    const performanceMonitor = PerformanceMonitor.getInstance();
    const fpsMetric = performanceMonitor.getMetric(PerformanceMetricType.FPS);
    const fps = fpsMetric ? fpsMetric.value : 60; // 默认值
    const targetFPS = 60;

    // 如果FPS低于目标，尝试优化设置
    if (fps < targetFPS * 0.8) {
      // 如果FPS非常低，禁用一些高级功能
      if (fps < targetFPS * 0.5) {
        this.useMultiLevelCulling = false;
        this.usePredictiveCulling = false;
        this.useTemporalCoherence = true;
        this.useConservativeCulling = true;
      } else {
        // 否则，只禁用预测剔除
        this.usePredictiveCulling = false;
        this.useMultiLevelCulling = true;
        this.useTemporalCoherence = true;
        this.useConservativeCulling = false;
      }

      // 选择最佳算法
      this.selectBestAlgorithm();
    } else if (fps > targetFPS * 1.2) {
      // 如果FPS很高，可以启用更多高级功能
      this.useMultiLevelCulling = true;
      this.usePredictiveCulling = true;
      this.useTemporalCoherence = true;
      this.useConservativeCulling = false;
    }
  }

  /**
   * 执行粗略剔除
   * @param camera 相机
   * @param scene 场景
   */
  private performCoarseCulling(camera: Camera, scene: Scene): void {
    // 使用视锥体剔除进行粗略剔除
    if (!this.world) {
      return;
    }

    // 获取相机视锥体
    const frustum = new THREE.Frustum();
    const projScreenMatrix = new THREE.Matrix4();
    projScreenMatrix.multiplyMatrices(
      camera.getThreeCamera().projectionMatrix,
      camera.getThreeCamera().matrixWorldInverse
    );
    frustum.setFromProjectionMatrix(projScreenMatrix);

    // 遍历所有实体进行视锥体剔除
    const entities = this.world.getEntities();
    for (const entity of entities.values()) {
      const cullableComponent = entity.getComponent('CullableComponent');
      if (cullableComponent) {
        const transform = entity.getComponent('Transform') as Transform;
        if (!transform) {
          continue;
        }

        const position = transform.getWorldPosition();

        // 检查是否在视锥体内
        if (!frustum.containsPoint(position)) {
          this.cullEntitySafe(entity);
        }
      }
    }
  }

  /**
   * 安全地剔除实体
   * @param entity 实体
   */
  private cullEntitySafe(entity: Entity): void {
    this.culledEntities.add(entity);
    const cullableComponent = entity.getComponent('CullableComponent');
    if (cullableComponent) {
      this.cullableComponents.set(entity, cullableComponent);
    }
  }

  /**
   * 执行预测剔除
   * @param camera 相机
   * @param scene 场景
   * @param deltaTime 帧间隔时间（秒）
   */
  private performPredictiveCulling(camera: Camera, scene: Scene, deltaTime: number): void {
    // 使用预测的相机位置进行剔除
    // 由于Camera没有clone方法，我们创建一个临时的相机位置
    const originalPosition = camera.getThreeCamera().position.clone();

    // 临时设置预测位置
    camera.getThreeCamera().position.copy(this.predictedCameraPosition);
    camera.getThreeCamera().updateMatrixWorld();

    // 使用预测相机进行剔除
    this.performMainOcclusionCulling(camera, scene);

    // 恢复原始位置
    camera.getThreeCamera().position.copy(originalPosition);
    camera.getThreeCamera().updateMatrixWorld();
  }

  /**
   * 更新统计信息
   * @param cullingTime 剔除时间（毫秒）
   */
  private updateStats(cullingTime: number): void {
    // 计算统计信息
    const totalObjects = this.cullableComponents.size;
    const culledObjects = this.culledEntities.size;
    const cullingRate = totalObjects > 0 ? culledObjects / totalObjects : 0;

    // 获取渲染时间指标
    const renderTimeMetric = PerformanceMonitor.getInstance().getMetric(PerformanceMetricType.RENDER_TIME);
    const renderTime = renderTimeMetric ? renderTimeMetric.value : 16; // 默认16ms
    const totalTime = cullingTime + renderTime;

    // 创建统计对象
    const stats: OcclusionCullingStats = {
      totalObjects,
      culledObjects,
      cullingRate,
      cullingTime,
      renderTime,
      totalTime,
      algorithm: this.algorithm,
      timestamp: Date.now()
    };

    // 添加到历史记录
    this.statsHistory.push(stats);

    // 限制历史记录长度
    if (this.statsHistory.length > this.maxHistoryLength) {
      this.statsHistory.shift();
    }

    // 发出统计更新事件
    this.eventEmitter.emit(EnhancedOcclusionCullingSystemEventType.STATS_UPDATED, stats);
  }

  /**
   * 获取统计历史
   * @returns 统计历史
   */
  public getStatsHistory(): OcclusionCullingStats[] {
    return this.statsHistory;
  }

  /**
   * 获取最新统计
   * @returns 最新统计
   */
  public getLatestStats(): OcclusionCullingStats | null {
    if (this.statsHistory.length === 0) {
      return null;
    }
    return this.statsHistory[this.statsHistory.length - 1];
  }

  /**
   * 添加事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public addEventListener(type: string, listener: EventCallback): void {
    this.eventEmitter.on(type, listener);
  }

  /**
   * 移除事件监听器
   * @param type 事件类型
   * @param listener 监听器
   */
  public removeEventListener(type: string, listener?: EventCallback): void {
    this.eventEmitter.off(type, listener);
  }
}
